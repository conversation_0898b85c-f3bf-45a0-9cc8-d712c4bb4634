# Financial Data Analysis - SQL Database System

## Overview

This project contains SQL scripts for managing time-based bucket configurations used in financial data analysis. The system is designed to support multiple bucket versions, allowing for different time interval configurations for data aggregation and analysis.

## Project Structure

```
data/
├── sql/
│   ├── create_bucket_tables.sql      # Database schema creation
│   ├── get es data from IBDB.sql     # ES data extraction queries
│   ├── group es by 10s to get volume.sql  # Volume aggregation queries
│   ├── join SPX with es volume.sql   # SPX and ES data joining
│   └── test.sql                      # Testing and validation queries
└── README.md                         # This documentation file
```

## Database Schema

### Core Tables

#### 1. `bucket_version`
Stores different versions of bucket configurations.

| Column | Type | Description |
|--------|------|-------------|
| `id` | INT | Auto-incrementing primary key |
| `version` | NVARCHAR(255) | Version name (e.g., 'Default', 'Custom_1') |
| `created_at` | DATETIME2 | Timestamp when record was created |
| `updated_at` | DATETIME2 | Timestamp when record was last updated |

#### 2. `bucket_spec`
Stores individual time bucket specifications for each version.

| Column | Type | Description |
|--------|------|-------------|
| `id` | INT | Auto-incrementing primary key |
| `bucket_start` | NVARCHAR(10) | Start time of bucket (Format: HH:MM:SS) |
| `bucket_version_id` | INT | Foreign key to bucket_version table |
| `created_at` | DATETIME2 | Timestamp when record was created |
| `updated_at` | DATETIME2 | Timestamp when record was last updated |

### Relationships
- `bucket_spec.bucket_version_id` → `bucket_version.id` (Foreign Key with CASCADE DELETE)

### Indexes
- `IX_bucket_spec_version_id` on `bucket_spec(bucket_version_id)` for performance optimization

## SQL Scripts Documentation

### 1. `create_bucket_tables.sql`
**Purpose**: Creates the initial database schema for bucket management.

**Features**:
- Safe table creation with existence checks
- Automatic sample data insertion
- Performance optimization with indexes
- Comprehensive error handling

**Sample Data**:
- **Default Version**: 5 buckets starting from 15:50:00 (30-second intervals)
- **Custom_1 Version**: 5 buckets starting from 15:45:00 (30-second intervals)

### 2. `get es data from IBDB.sql`
**Purpose**: Extracts ES (E-mini S&P 500 futures) data from the IBDB database.

### 3. `group es by 10s to get volume.sql`
**Purpose**: Aggregates ES data into 10-second intervals to calculate volume metrics.

### 4. `join SPX with es volume.sql`
**Purpose**: Joins SPX (S&P 500 index) data with ES volume data for correlation analysis.

### 5. `test.sql`
**Purpose**: Contains testing and validation queries for the database system.

## Usage Instructions

### Initial Setup
1. **Run the schema creation script**:
   ```sql
   EXEC create_bucket_tables.sql
   ```

2. **Verify the setup**:
   ```sql
   -- Check if tables were created
   SELECT * FROM bucket_version;
   SELECT * FROM bucket_spec;
   ```

### Working with Bucket Versions

#### Create a New Bucket Version
```sql
INSERT INTO bucket_version (version) VALUES ('My_Custom_Version');
```

#### Add Buckets to a Version
```sql
-- Get the version ID first
DECLARE @version_id INT = (SELECT id FROM bucket_version WHERE version = 'My_Custom_Version');

-- Add buckets (30-second intervals starting at 14:30:00)
INSERT INTO bucket_spec (bucket_start, bucket_version_id) VALUES 
('14:30:00', @version_id),
('14:30:30', @version_id),
('14:31:00', @version_id);
```

#### Query Buckets for a Specific Version
```sql
SELECT 
    bv.version,
    bs.bucket_start,
    DATEADD(SECOND, 30, CAST(bs.bucket_start AS TIME)) as bucket_end
FROM bucket_version bv
JOIN bucket_spec bs ON bv.id = bs.bucket_version_id
WHERE bv.version = 'Default'
ORDER BY bs.bucket_start;
```

## Data Analysis Workflow

1. **Setup**: Run `create_bucket_tables.sql` to establish the database schema
2. **Extract**: Use `get es data from IBDB.sql` to extract raw ES data
3. **Aggregate**: Apply `group es by 10s to get volume.sql` for volume calculations
4. **Correlate**: Use `join SPX with es volume.sql` for cross-asset analysis
5. **Validate**: Run `test.sql` to ensure data integrity

## Bucket System Design

### Time Bucket Concept
Each bucket represents a 30-second time interval used for data aggregation:
- **Bucket Start**: The beginning time of the interval (e.g., 15:50:00)
- **Bucket End**: Automatically calculated as start + 30 seconds (e.g., 15:50:30)
- **Version Management**: Multiple bucket configurations can coexist

### Use Cases
- **Market Analysis**: Aggregate trading volume into consistent time intervals
- **Cross-Asset Correlation**: Align data from different instruments to the same time buckets
- **Backtesting**: Apply different bucket configurations for strategy testing
- **Real-time Processing**: Use predefined buckets for live data aggregation

## Performance Considerations

- **Indexes**: Foreign key index optimizes joins between tables
- **Cascade Deletes**: Removing a version automatically removes associated buckets
- **Time Format**: NVARCHAR(10) for bucket_start allows efficient string operations
- **Batch Operations**: Sample data insertion uses batch INSERT statements

## Maintenance

### Regular Tasks
1. **Monitor Performance**: Check query execution plans for bucket-related queries
2. **Data Validation**: Run test queries to ensure data integrity
3. **Version Management**: Archive old versions and clean up unused bucket specifications

### Backup Strategy
- Backup both tables before major schema changes
- Consider versioning bucket configurations for rollback capability

## Troubleshooting

### Common Issues
1. **Duplicate Versions**: Check for existing versions before creating new ones
2. **Invalid Time Formats**: Ensure bucket_start follows HH:MM:SS format
3. **Foreign Key Violations**: Verify bucket_version_id exists before inserting bucket_spec records

### Debug Queries
```sql
-- Check for orphaned bucket specifications
SELECT bs.* FROM bucket_spec bs
LEFT JOIN bucket_version bv ON bs.bucket_version_id = bv.id
WHERE bv.id IS NULL;

-- Validate time format consistency
SELECT bucket_start FROM bucket_spec 
WHERE bucket_start NOT LIKE '[0-9][0-9]:[0-9][0-9]:[0-9][0-9]';
```

## Future Enhancements

### Potential Improvements
1. **Dynamic Bucket Sizes**: Support for variable interval lengths
2. **Time Zone Support**: Add timezone awareness for global markets
3. **Audit Trail**: Enhanced logging for bucket configuration changes
4. **API Integration**: REST endpoints for bucket management
5. **Real-time Updates**: WebSocket support for live bucket modifications

### Schema Extensions
- Add `bucket_end` column for explicit end time storage
- Include `description` field for bucket version documentation
- Add `is_active` flag for version management
- Implement `created_by` and `updated_by` for user tracking

## Contributing

When contributing to this project:
1. Follow the existing commenting style
2. Add appropriate error handling
3. Include test queries for new functionality
4. Update this README for any schema changes
5. Document any new bucket versions or configurations

## License

[Add your license information here]

## Contact

[Add your contact information here] 