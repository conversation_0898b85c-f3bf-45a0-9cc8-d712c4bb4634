import httpx  # install via pip install httpx
import csv

BASE_URL = "http://127.0.0.1:25510/v2"  # all endpoints use this URL base

# set params
params = {
}
#
# This is the non-streaming version, and the entire response
# will be held in memory.
#
url = BASE_URL + '/at_time/option/quote?root=SPY&exp=20240119&strike=470000&right=C&start_date=20240116&end_date=20240116&ivl=44100000'

response = httpx.get(url, params=params, timeout=60)  # make the request
response.raise_for_status()  # make sure the request worked

# read the entire response, and parse it as CSV
csv_reader = csv.reader(response.text.split("\n"))

for row in csv_reader:
    print(row)  # do something with the data


#
# This is the streaming version, and will read line-by-line
#
url = BASE_URL + '/at_time/option/quote?root=SPY&exp=20240119&strike=470000&right=C&start_date=20240116&end_date=20240116&ivl=44100000'

with httpx.stream("GET", url, params=params, timeout=60) as response:
    response.raise_for_status()  # make sure the request worked
    for line in response.iter_lines():
        print(line)  # do something with the data
