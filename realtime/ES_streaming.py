from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from datetime import datetime, date
from dateutil.tz import tzlocal

import datetime
import json
import os
import sys
import threading
import time
import pyodbc

# Database connection on Agent3(P244).
dbconnection_Agent3 = pyodbc.connect('DRIVER={SQL Server}; SERVER=127.0.0.1; DATABASE=StocksDB; Trusted_Connection=YES')
cursor_Agent3 = dbconnection_Agent3.cursor()

# Database connection on Agent?(E170).
dbcon_AE170 = pyodbc.connect('DRIVER={SQL Server}; SERVER=70.231.62.170; DATABASE=ALGO_DATA; UID=sa; PWD=**************')
cursor_AE170 = dbcon_AE170.cursor()

class IBapi_ES(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)

        ''' Set start tag to 0 to ignore data before market open. '''
        self.start_tag = 0
        self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask = 0, 0, 0, 0

        ''' vol_Diff is to record the first Volume which without price as the difference. '''
        self.vol_Diff, self.sum_Size = 0, 0
        self.sum_List, self.vol_List = [], []

        self.symbol, self.server_Time = "NULL", "0000-00-00 00:00:00"
        self.ESMonth = ['F', 'G', 'H', 'J', 'K', 'M', 'N', 'Q', 'U', 'V', 'X', 'Z']    # These symbols present month from Jan to Dec.


    def error(self, reqId, errorCode, errorString):
        self.error_Info = "ES Connection Info: %s %s %s" % (str(reqId), str(errorCode), errorString)
        print(self.error_Info)

        if "Max rate of messages per second has been exceeded" in self.error_Info:
            self.error_Time = DTCheck().localDT()
            with open("sysinfo.txt", "a+", encoding = "utf-8") as output:
                output.write(self.error_Time + ", " + self.error_Info + "\n")
            SelfRestart().reStart()    # Streaming process will auto-restart once Max rate error occurs.


    def currentTime(self, time):
        self.server_Time = datetime.datetime.fromtimestamp(time).strftime("%Y-%m-%d %H:%M:%S")    # Convert timestamp to datetime format.
        return self.server_Time


    def symbolSamples(self, reqId, contractDescriptions):
        super().symbolSamples(reqId, contractDescriptions)
        # print("Symbol result, request ID:", reqId)
        if contractDescriptions != []:
            if contractDescriptions[0].contract.symbol == "ES":
                self.symbol = contractDescriptions[0].contract.symbol


    def tickPrice(self, reqId, tickType, price, attrib):
        self.type = TickTypeEnum.to_str(tickType)
        self.price = Tick(self.type, price)

        ''' Request symbol of current type. '''
        # self.reqMatchingSymbols(11, "ES")

        ''' Request exchange Code.
            The tick types 'bidExch' (tick type 32), 'askExch' (tick type 33), 'lastExch' (tick type 84) are used to identify the source of a quote.
            Exchange map has a different code, such as "a6" or "a9". '''
        # self.reqSmartComponents(84, "a6")    # Parameters to be decided.

        ''' Request current server time. The time string which initialled will be changed evertime when Price and Size Def been called. '''
        self.reqCurrentTime()

        if self.type == "CLOSE":
            self.start_tag = 1

        elif self.type == "BID" and self.start_tag == 1:
            self.rec_Bid = price

            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Bid, self.type + ":" + str(price))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Bid, self.type + ":" + str(price))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

        elif self.type == "ASK" and self.start_tag == 1:
            self.rec_Ask = price

            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Ask, self.type + ":" + str(price))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Ask, self.type + ":" + str(price))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

        elif self.type == "LAST" and self.start_tag == 1:
            if self.rec_Last != price and self.rec_Last != 0 and self.sum_List != []:
                for i in self.sum_List:
                    self.sum_Size = self.sum_Size + i

                self.rec_Size = self.sum_Size
                # print("\n*** Price updates from %s to %s. ***" % (str(self.rec_Last), price))

                ''' Part 1: When Last price updates befor Volume, let the upper edge number plus recent Size amount, like a queue.
                            vol_Size is the last volume reference value after plus rec_Size. '''

                if len(self.vol_List) == 1:
                    self.vol_Base = self.vol_List[0] + self.rec_Size
                    self.vol_List.append(self.vol_Base)
                    self.vol_Size = self.vol_Base
                elif len(self.vol_List) == 2:
                    self.vol_Base = self.vol_List[1] + self.rec_Size
                    self.vol_List.pop(0)
                    self.vol_List.append(self.vol_Base)
                    self.vol_Size = self.vol_Base
                else:
                    self.vol_Size = 0

                if target_ES:
                    self.symbol_ES = ("'%s'" % (target_ES.symbol))
                else:
                    self.symbol_ES = "'ES'"

                self.es_stat = DBQuery().dbExec("StocksDB.dbo.ES_Streaming", self.server_Time, self.symbol_ES, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, self.vol_Size)
                cursor_Agent3.execute(self.es_stat)
                cursor_Agent3.connection.commit()

                self.es_stat_A170 = DBQuery().dbExec("ALGO_DATA.dbo.ES_Streaming", self.server_Time, self.symbol_ES, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, self.vol_Size)
                cursor_AE170.execute(self.es_stat_A170)
                cursor_AE170.connection.commit()

                self.sum_List = []
                self.sum_Size, self.rec_Size, self.vol_Size = 0, 0, 0

            self.rec_Last = price

            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.type + ":" + str(price))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.type + ":" + str(price))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()


    def tickSize(self, reqId, tickType, size):
        self.size_Type = TickTypeEnum.to_str(tickType)
        self.size = Tick(self.size_Type, size)
        self.reqCurrentTime()

        if self.size_Type == "BID_SIZE" and size != 0 and self.start_tag == 1:
            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Bid, self.size_Type + ":" + str(size))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Bid, self.size_Type + ":" + str(size))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

        elif self.size_Type == "ASK_SIZE" and size != 0 and self.start_tag == 1:
            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Ask, self.size_Type + ":" + str(size))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Ask, self.size_Type + ":" + str(size))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

        elif self.size_Type == "LAST_SIZE" and size != 0 and self.start_tag == 1:
            # print("\n*** Recent type: LAST, at price %s. ***" % (str(self.rec_Last)))
            if self.sum_List != [] and len(self.sum_List) == 1 and self.sum_List[0] == size:
                pass
            else:
                self.sum_List.append(size)

            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()
            # print(self.server_Time + ", " + self.size_Type, ":", size, end='\n')

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

        elif self.size_Type == "VOLUME" and size != 0 and self.start_tag == 1:
            self.rec_Size = size

            ''' Part 2: When Volume appears, add the size value to list.
                        Volume list has two values, which are the recent two Volume values, the length of list will NOT more than two.
                        When new Volume comes, pop the first then add the new one, which is like a queue.
                        Once the value amount changes, the recent size is the difference which new Volume minus previous one. '''

            if len(self.vol_List) == 2:
                self.vol_List.pop(0)
            self.vol_List.append(size)

            ''' Size calculation. '''
            for i in self.sum_List:
                self.sum_Size = self.sum_Size + i

            if len(self.vol_List) == 2:
                self.vol_Diff = self.vol_List[1] - self.vol_List[0]


            ''' Recent size: The Sum size value will most likely to be LESS than the Volume difference.
                             It seems impossible for the Sum one LARGER than the difference,
                             since the new Volume should always larger than the old one plus all size change. '''

            self.rec_Size = self.vol_Diff

            self.raw_stat = DBQuery().dbRaw("StocksDB.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor_Agent3.execute(self.raw_stat)
            cursor_Agent3.connection.commit()

            self.raw_stat_A170 = DBQuery().dbRaw("ALGO_DATA.dbo.ES_Streaming_Raw", self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor_AE170.execute(self.raw_stat_A170)
            cursor_AE170.connection.commit()

            if target_ES:
                self.symbol_ES = ("'%s'" % (target_ES.symbol))
            else:
                self.symbol_ES = "'ES'"

            # Skip those without volume change.
            if self.rec_Size != 0:
                self.es_stat = DBQuery().dbExec("StocksDB.dbo.ES_Streaming", self.server_Time, self.symbol_ES, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, str(size))
                cursor_Agent3.execute(self.es_stat)
                cursor_Agent3.connection.commit()

                self.es_stat_A170 = DBQuery().dbExec("ALGO_DATA.dbo.ES_Streaming", self.server_Time, self.symbol_ES, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, str(size))
                cursor_AE170.execute(self.es_stat_A170)
                cursor_AE170.connection.commit()

            ''' Part 3: When LAST data inserted to DB, it is needed to set the large value as a small one,
                        since the Volume value is based on the upper edge one. '''

            if len(self.vol_List) == 2 and self.vol_List[0] < self.vol_List[1]:
                self.vol_List[0] = self.vol_List[1]

            self.sum_List = []
            self.sum_Size, self.rec_Size = 0, 0


    def smartComponents(self, reqId, smartComponentMap):
        super().smartComponents(reqId, smartComponentMap)
        # print(smartComponentMap)
        # for smartComponent in smartComponentMap:
        #     print("SmartComponent:", smartComponent)


    def typeParser(self, type):
        self.type_Value = ", ".join(type)

        if "BID" in type[1]:
            self.bid_Value = ", ".join(self.bid_Size)
            self.VList.append([self.type_Value + ", " + self.bid_Value])
        elif "ASK" in type[1]:
            self.ask_Value = ", ".join(self.ask_Size)
            self.VList.append([self.type_Value + ", " + self.ask_Value])
        elif "LAST" in type[1]:
            self.last_Value = ", ".join(self.last_Size)
            self.VList.append([self.type_Value + ", " + self.last_Value])
        return self.VList


class Tick(object):
    def __init__(self, type, value):
        self.type = type
        self.value = value


class DTCheck(object):
    def expireDate(self, date):
        with open('contract.json', 'r') as file:
            self.content = json.load(file)['contract']

        for data in self.content:
            if data['symbol'] == 'ES':
                self.expList = data['expire']
                for expDate in self.expList:
                    if int(date) <= int(expDate):
                        return expDate


    def localDT(self):
        tzone = datetime.datetime.now(tzlocal()).tzname()
        if tzone == "China Standard Time":
            date = datetime.datetime.now() - datetime.timedelta(hours=12)   # U.S. East time zone.
        else:
            date = datetime.datetime.now()
        return str(date)


class DBQuery(object):
    def dbRaw(self, database, date, price, content):
        self.time, self.type, self.value = "'" + date + "'", "'" + content.split(":")[0] + "'", content.split(":")[1]
        self.raw_statement = "INSERT INTO %s (timeStamp,attribute,value) VALUES (%s,%s,%s)" % (database, self.time,self.type,self.value)    # CoreTradingSmall / Stocks
        return self.raw_statement


    def dbExec(self, database, date, symbol, price, size, bid, ask, volume):
        self.symbol = symbol
        # self.sequence = int(date[23:])
        self.eventYear, self.eventMonth, self.eventDay = int(date[0:4]), int(date[5:7]), int(date[8:10])
        self.eventHour, self.eventMinute, self.eventSecond = int(date[11:13]), int(date[14:16]), int(date[17:19])
        # self.eventMS = int(str(round(float(date[19:]), 3))[2:])

        if self.symbol == "'ES'":
            self.size, self.targetDB = size * 100, database    # "StocksDB.dbo.ES_Streaming" / CoreTradingSmall / Stocks

        self.statement = "INSERT INTO %s (symbol,sequence,exchange,price,size,bidPrice,askPrice,volume,flags,eventYear,eventMonth,eventDay,eventHour,eventMinute,eventSecond,eventMS) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)" \
                            % (self.targetDB,self.symbol,0,0,price,self.size,bid,ask,volume,0,self.eventYear,self.eventMonth,self.eventDay,self.eventHour,self.eventMinute,self.eventSecond,0)
        return self.statement


class SelfRestart(object):
    def reStart(self):
        os.execl(sys.executable, os.path.abspath(__file__), * sys.argv)


class RunApp(object):
    def ESLoop():
        app_ES.run()



if __name__ == "__main__":
    currentDT, secDiff = DTCheck().localDT()[0:19], 300    # Give a time period difference to the end of 20:00 for each day.

    tarDate = currentDT.split(' ')[0].replace('-', '')
    # expDate = DTCheck().expireDate(tarDate)
    # expDate = int(str(tarDate)[0:-2])
    expDate = 202509

    # tarTime = currentDT.split(' ')[0] + ' ' + '20:00:00'
    # totalSec = (datetime.datetime.strptime(tarTime, '%Y-%m-%d %H:%M:%S') - datetime.datetime.strptime(currentDT, '%Y-%m-%d %H:%M:%S')).seconds + secDiff

    ''' Subscripition 3:
        ES live data request, Id starts from 3(30). '''
    app_ES = IBapi_ES()
    app_ES.connect('127.0.0.1', 7497, 3)

    api_thread_ES = threading.Thread(target=RunApp.ESLoop, daemon=True)
    api_thread_ES.start()
    time.sleep(1)   # Sleep interval to allow time for connection to server.

    target_ES = Contract()
    target_ES.symbol, target_ES.secType, target_ES.exchange, target_ES.currency, target_ES.lastTradeDateOrContractMonth = "ES", "FUT", "CME", "USD", expDate
    app_ES.reqMktData(3, target_ES, '', False, False, [])

    ''' Daily seconds: 119.5 Hours from Sunday 17:00 to Friday 16:00, totally (4 * 24 + 23) * 3600 + 900 * 2 = 430200 seconds.
                       Give 15 minutes margins to the start and end. Ignore the brief halt in trading from 15:15 to 15:30. '''
    # time.sleep(430200)    # totalSec

    ''' 2025.04.15 Updates:
        The process needs to be restart per day, since the connection will be closed(NOT lost) every evening after TWS restarts.
        Use the windows schedule task to run this script every day at 00:01, lasts 24 hours which are 86400 seconds, then minus 100 seconds due to TWS restarts at 23:59. '''
    time.sleep(86300)    # totalSec
    app_ES.disconnect()