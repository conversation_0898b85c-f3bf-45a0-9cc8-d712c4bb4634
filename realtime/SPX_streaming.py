from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from datetime import datetime, date
from dateutil.tz import tzlocal

import csv
import datetime
import threading
import time
import pyodbc

# dbconnection = pyodbc.connect('DRIVER={SQL Server Native Client 11.0}; SERVER=SHIHYAT\ShihYat; DATABASE=Stocks; Trusted_Connection=YES')
# dbconnection = pyodbc.connect('DRIVER={SQL Server}; SERVER=192.168.1.99; DATABASE=TongDB; UID=sa; PWD=**************')
dbconnection = pyodbc.connect('DRIVER={SQL Server}; SERVER=127.0.0.1; DATABASE=StocksDB; Trusted_Connection=YES')
cursor = dbconnection.cursor()

# Database connection on Agent?(E170).
dbcon_AE170 = pyodbc.connect('DRIVER={SQL Server}; SERVER=70.231.62.170; DATABASE=ALGO_DATA; UID=sa; PWD=**************')
cursor_AE170 = dbcon_AE170.cursor()

class IBapi_SPX(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)

        ''' Set start tag to 0 to ignore data before market open. '''
        self.start_tag = 0

        self.symbol, self.SPX_Time = "NULL", "0000-00-00 00:00:00"
        self.open, self.high, self.low, self.close = 0, 0, 0, 0
        self.hourRecord, self.minRecord = 0, 0


    def error(self, reqId, errorCode, errorString):
        print("SPX Connection Info:", reqId, errorCode, errorString)


    def symbolSamples(self, reqId, contractDescriptions):
        super().symbolSamples(reqId, contractDescriptions)
        if contractDescriptions != []:
            if contractDescriptions[0].contract.symbol == "SPX":
                self.symbol = contractDescriptions[0].contract.symbol


    def currentTime(self, time):
        self.SPX_Time = datetime.datetime.fromtimestamp(time).strftime("%Y-%m-%d %H:%M:%S")    # Convert timestamp to datetime format.
        return self.SPX_Time


    def tickPrice(self, reqId, tickType, price, attrib):
        self.type = TickTypeEnum.to_str(tickType)
        self.price = Tick(self.type, price)
        self.reqCurrentTime()

        if self.type == "CLOSE":
            self.start_tag = 1

        elif self.type == "LAST" and self.start_tag == 1:
            if target_SPX:
                self.symbol_SPX = ("'%s'" % (target_SPX.symbol))
            else:
                self.symbol_SPX = "'SPX'"

            self.dateInfo, self.timeInfo = self.SPX_Time[0:10], self.SPX_Time[11:19]
            self.hourInfo, self.minInfo, self.secInfo = self.timeInfo[0:2], self.timeInfo[3:5], self.timeInfo[6:8]

            if self.high == 0:
                self.high = price
            if self.low == 0:
                self.low = price

            self.close = price

            if self.high < price and self.high != 0:
                self.high = price
            if self.low > price and self.low != 0:
                self.low = price

            if self.minRecord != self.minInfo:
                # print(f'*** Time: {self.SPX_Time}, Type: {self.type}, SPX: {price}. ***')
                self.SPX_Statement = DBQuery().dbExec("StocksDB.dbo.SPX_Streaming", self.dateInfo, self.timeInfo, self.open, self.high, self.low, self.close)
                cursor.execute(self.SPX_Statement)
                cursor.connection.commit()

                self.SPX_Statement_AE170 = DBQuery().dbExec("ALGO_DATA.dbo.SPX_Streaming", self.dateInfo, self.timeInfo, self.open, self.high, self.low, self.close)
                cursor_AE170.execute(self.SPX_Statement_AE170)
                cursor_AE170.connection.commit()

                self.hourRecord, self.minRecord = self.hourInfo, self.minInfo
                self.open, self.close = price, 0
                self.high, self.low = 0, 0

            # print(self.SPX_Time + ", SPX:", price, end='\n')
            self.SPX_Raw_Statement = DBQuery().dbExecRaw("StocksDB.dbo.SPX_Streaming_Raw", self.SPX_Time, self.symbol_SPX, price)
            cursor.execute(self.SPX_Raw_Statement)
            cursor.connection.commit()

            self.SPX_Raw_Statement_AE170 = DBQuery().dbExecRaw("ALGO_DATA.dbo.SPX_Streaming_Raw", self.SPX_Time, self.symbol_SPX, price)
            cursor_AE170.execute(self.SPX_Raw_Statement_AE170)
            cursor_AE170.connection.commit()


    def LastMinPrice(self):
        if self.start_tag == 1 and self.open != 0 and self.close != 0:
            self.reqCurrentTime()
            self.dateInfo = self.SPX_Time[0:10]

            self.lastMinuteValue = DBQuery().LastCheck("StocksDB.dbo.SPX_Streaming_Raw", self.dateInfo)
            self.open, self.close, self.high, self.low = self.lastMinuteValue[0], self.lastMinuteValue[1], self.lastMinuteValue[2], self.lastMinuteValue[3]
            self.lastMinute = self.lastMinuteValue[4]

            if int(self.lastMinute) < 10:
                self.lastMinute = "0" + str(self.lastMinute)
            self.timeInfo = self.SPX_Time[11:14] + str(self.lastMinute) + ":00"

            print(f'*** Last Minute Price: {self.close} at {self.SPX_Time} inserted. ***')
            self.SPX_Last_Statement = DBQuery().dbExec("StocksDB.dbo.SPX_Streaming", self.dateInfo, self.timeInfo, self.open, self.high, self.low, self.close)
            cursor.execute(self.SPX_Last_Statement)
            cursor.connection.commit()

            self.SPX_Last_Statement_AE170 = DBQuery().dbExec("ALGO_DATA.dbo.SPX_Streaming", self.dateInfo, self.timeInfo, self.open, self.high, self.low, self.close)
            cursor_AE170.execute(self.SPX_Last_Statement_AE170)
            cursor_AE170.connection.commit()


class Tick(object):
    def __init__(self, type, value):
        self.type = type
        self.value = value


class DTCheck(object):
    def localDT(self):
        tzone = datetime.datetime.now(tzlocal()).tzname()
        if tzone == "China Standard Time":
            date = datetime.datetime.now() - datetime.timedelta(hours=13)   # U.S. East time zone.
        else:
            date = datetime.datetime.now()
        return str(date)


class DBQuery(object):
    def dbExec(self, database, date, time, open, high, low, close):
        self.targetDB = database    # "StocksDB.dbo.SPX_Streaming"
        self.statement = "INSERT INTO %s (Date,Time,[Open],High,Low,[Close]) VALUES (%s,%s,%s,%s,%s,%s)" \
                            % (self.targetDB,"'" + date + "'","'" + str(datetime.datetime.strptime(time, "%H:%M:%S") - datetime.timedelta(minutes = 1))[11:16] + "'",float(open),float(high),float(low),float(close))
        return self.statement


    def dbExecRaw(self, database, date, symbol, price):
        self.symbol = symbol
        self.eventYear, self.eventMonth, self.eventDay = int(date[0:4]), int(date[5:7]), int(date[8:10])
        self.eventHour, self.eventMinute, self.eventSecond = int(date[11:13]), int(date[14:16]), int(date[17:19])

        if self.symbol == "'SPX'":
            self.size, self.targetDB = 0, database    # "StocksDB.dbo.SPX_Streaming_Raw"

        self.statement = "INSERT INTO %s (Symbol,Price,Year,Month,Day,Hour,Minute,Second) VALUES (%s,%s,%s,%s,%s,%s,%s,%s)" \
                            % (self.targetDB,self.symbol,float(price),self.eventYear,self.eventMonth,self.eventDay,self.eventHour,self.eventMinute,self.eventSecond)
        return self.statement


    def LastCheck(self, database, date):
        self.eventYear, self.eventMonth, self.eventDay = int(date[0:4]), int(date[5:7]), int(date[8:10])
        self.statement = "SELECT TOP(1) [Minute] FROM %s WHERE Year=%s and Month=%s and Day=%s ORDER BY PKID DESC" \
                            % (database, self.eventYear, self.eventMonth, self.eventDay)
        cursor.execute(self.statement)
        self.LastMinute = cursor.fetchall()[0][0]

        self.LMP_Statement = "SELECT [Price] FROM %s WHERE Year=%s AND Month=%s AND Day=%s AND Hour=%s AND Minute=%s ORDER BY PKID DESC" \
                                % (database, self.eventYear, self.eventMonth, self.eventDay, 16, self.LastMinute)
        cursor.execute(self.LMP_Statement)
        self.LP_List = [row[0] for row in cursor.fetchall()]

        if self.LP_List:
            self.open, self.close = self.LP_List[-1], self.LP_List[0]
            self.high, self.low = max(self.LP_List), min(self.LP_List)
            return [self.open, self.close, self.high, self.low, int(self.LastMinute) + 1]
        else:
            print("*** No Valid Data. ***")


class RunApp(object):
    def SPXLoop():
        app_SPX.run()



if __name__ == "__main__":
    currentDT, secDiff = DTCheck().localDT()[0:19], 300    # Give a time period difference to the end of 17:00 for each day.

    # tarTime = currentDT.split(' ')[0] + ' ' + '20:00:00'
    # totalSec = (datetime.datetime.strptime(tarTime, '%Y-%m-%d %H:%M:%S') - datetime.datetime.strptime(currentDT, '%Y-%m-%d %H:%M:%S')).seconds + secDiff

    ''' Subscripition 1:
        SPX live data request, Id starts from 1(10). '''
    app_SPX = IBapi_SPX()
    app_SPX.connect('127.0.0.1', 7497, 1)

    api_thread_SPX = threading.Thread(target=RunApp.SPXLoop, daemon=True)
    api_thread_SPX.start()
    time.sleep(1)   # Sleep interval to allow time for connection to server.

    target_SPX = Contract()
    target_SPX.symbol, target_SPX.secType, target_SPX.exchange, target_SPX.currency = "SPX", "IND", "CBOE", "USD"
    app_SPX.reqMktData(1, target_SPX, '', False, False, [])

    ''' Daily seconds: 7.25 Hours from 9:15 to 16:30, totally 7.25 * 3600 = 26100 seconds. Give 15 minutes margins to the start and end. '''
    time.sleep(24900)    # totalSec
    # time.sleep(17100)

    app_SPX.LastMinPrice()
    time.sleep(1099)

    app_SPX.disconnect()