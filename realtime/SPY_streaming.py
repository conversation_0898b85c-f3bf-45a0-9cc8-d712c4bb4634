from ibapi.client import EClient
from ibapi.wrapper import E<PERSON>rapper
from ibapi.contract import Contract
from ibapi.ticktype import TickType<PERSON><PERSON>
from datetime import datetime, date
from dateutil.tz import tzlocal

import datetime
import os
import sys
import threading
import time
import pyodbc

dbconnection = pyodbc.connect('DRIVER={SQL Server}; SERVER=127.0.0.1; DATABASE=StocksDB; Trusted_Connection=YES')
cursor = dbconnection.cursor()

class IBapi_SPY(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)

        ''' Set start tag to 0 to ignore data before market open. '''
        self.start_tag = 0
        self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask = 0, 0, 0, 0

        ''' vol_Diff is to record the first Volume which without price as the difference. '''
        self.vol_Diff, self.sum_Size = 0, 0
        self.sum_List, self.vol_List = [], []

        self.symbol, self.server_Time = "NULL", "0000-00-00 00:00:00"


    def error(self, reqId, errorCode, errorString):
        self.error_Info = "SPY Connection Info: %s %s %s" % (str(reqId), str(errorCode), errorString)
        print(self.error_Info)

        if "Max rate of messages per second has been exceeded" in self.error_Info:
            self.error_Time = DTCheck().localDT()
            with open("sysinfo.txt", "a+", encoding = "utf-8") as output:
                output.write(self.error_Time + ", " + self.error_Info + "\n")
            SelfRestart().reStart()    # Streaming process will auto-restart once Max rate error occurs.


    def symbolSamples(self, reqId, contractDescriptions):
        super().symbolSamples(reqId, contractDescriptions)
        # print("Symbol result, request ID:", reqId)
        if contractDescriptions != []:
            if contractDescriptions[0].contract.symbol == "SPY":
                self.symbol = contractDescriptions[0].contract.symbol


    def currentTime(self, time):
        self.server_Time = datetime.datetime.fromtimestamp(time).strftime("%Y-%m-%d %H:%M:%S")    # Convert timestamp to datetime format.
        return self.server_Time


    def tickPrice(self, reqId, tickType, price, attrib):
        self.type = TickTypeEnum.to_str(tickType)
        self.price = Tick(self.type, price)

        ''' Request symbol of current type. '''
        # self.reqMatchingSymbols(11, "SPY")

        ''' Request exchange Code.
            The tick types 'bidExch' (tick type 32), 'askExch' (tick type 33), 'lastExch' (tick type 84) are used to identify the source of a quote.
            Exchange map has a different code, such as "a6" or "a9". '''
        # self.reqSmartComponents(84, "a6")    # Parameters to be decided.

        ''' Request current server time. The time string which initialled will be changed evertime when Price and Size Def been called. '''
        self.reqCurrentTime()
        # print(self.server_Time + ", " + self.type, ":", price, end='\n')

        if self.type == "CLOSE":
            self.start_tag = 1

        elif self.type == "BID" and self.start_tag == 1:
            self.rec_Bid = price
            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Bid, self.type + ":" + str(price))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()
        elif self.type == "ASK" and self.start_tag == 1:
            self.rec_Ask = price
            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Ask, self.type + ":" + str(price))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()
        elif self.type == "LAST" and self.start_tag == 1:
            if self.rec_Last != price and self.rec_Last != 0 and self.sum_List != []:
                for i in self.sum_List:
                    self.sum_Size = self.sum_Size + i

                self.rec_Size = self.sum_Size
                # print("\n*** Price updates from %s to %s. ***" % (str(self.rec_Last), price))

                ''' Part 1: When Last price updates befor Volume, let the upper edge number plus recent Size amount, like a queue.
                            vol_Size is the last volume reference value after plus rec_Size. '''

                if len(self.vol_List) == 1:
                    self.vol_Base = self.vol_List[0] + self.rec_Size
                    self.vol_List.append(self.vol_Base)
                    self.vol_Size = self.vol_Base
                elif len(self.vol_List) == 2:
                    self.vol_Base = self.vol_List[1] + self.rec_Size
                    self.vol_List.pop(0)
                    self.vol_List.append(self.vol_Base)
                    self.vol_Size = self.vol_Base
                else:
                    self.vol_Size = 0

                if target_SPY:
                    self.symbol_SPY = ("'%s'" % (target_SPY.symbol))
                else:
                    self.symbol_SPY = "'SPY'"

                self.spy_insert = DBQuery().dbExec(self.server_Time, self.symbol_SPY, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, self.vol_Size)
                cursor.execute(self.spy_insert)
                cursor.connection.commit()

                self.sum_List = []
                self.sum_Size, self.rec_Size, self.vol_Size = 0, 0, 0

            self.rec_Last = price

            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Last, self.type + ":" + str(price))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()


    def tickSize(self, reqId, tickType, size):
        self.size_Type = TickTypeEnum.to_str(tickType)
        self.size = Tick(self.size_Type, size)
        self.reqCurrentTime()

        if self.size_Type == "BID_SIZE" and size != 0 and self.start_tag == 1:
            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Bid, self.size_Type + ":" + str(size))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()
        elif self.size_Type == "ASK_SIZE" and size != 0 and self.start_tag == 1:
            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Ask, self.size_Type + ":" + str(size))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()
        elif self.size_Type == "LAST_SIZE" and size != 0 and self.start_tag == 1:
            # print("\n*** Recent type: LAST, at price %s. ***" % (str(self.rec_Last)))
            if self.sum_List != [] and len(self.sum_List) == 1 and self.sum_List[0] == size:
                pass
            else:
                self.sum_List.append(size)

            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()
            # print(self.server_Time + ", " + self.size_Type, ":", size, end='\n')
        elif self.size_Type == "VOLUME" and size != 0 and self.start_tag == 1:
            self.rec_Size = size

            ''' Part 2: When Volume appears, add the size value to list.
                        Volume list has two values, which are the recent two Volume values, the length of list will NOT more than two.
                        When new Volume comes, pop the first then add the new one, which is like a queue.
                        Once the value amount changes, the recent size is the difference which new Volume minus previous one. '''
            if len(self.vol_List) == 2:
                self.vol_List.pop(0)
            self.vol_List.append(size)

            ''' Size calculation. '''
            for i in self.sum_List:
                self.sum_Size = self.sum_Size + i

            if len(self.vol_List) == 2:
                self.vol_Diff = self.vol_List[1] - self.vol_List[0]

            ''' Recent size: The Sum size value will most likely to be LESS than the Volume difference.
                             It seems impossible for the Sum one LARGER than the difference,
                             since the new Volume should always larger than the old one plus all size change. '''

            self.rec_Size = self.vol_Diff

            self.raw_insert = DBQuery().dbRaw(self.server_Time, self.rec_Last, self.size_Type + ":" + str(size))
            cursor.execute(self.raw_insert)
            cursor.connection.commit()

            if target_SPY:
                self.symbol_SPY = ("'%s'" % (target_SPY.symbol))
            else:
                self.symbol_SPY = "'SPY'"

            # Skip those without volume change.
            if self.rec_Size != 0:
                self.spy_insert = DBQuery().dbExec(self.server_Time, self.symbol_SPY, self.rec_Last, self.rec_Size, self.rec_Bid, self.rec_Ask, str(size))
                cursor.execute(self.spy_insert)
                cursor.connection.commit()

            ''' Part 3: When LAST data inserted to DB, it is needed to set the large value as a small one,
                        since the Volume value is based on the upper edge one. '''

            if len(self.vol_List) == 2 and self.vol_List[0] < self.vol_List[1]:
                self.vol_List[0] = self.vol_List[1]

            self.sum_List = []
            self.sum_Size, self.rec_Size = 0, 0


    def smartComponents(self, reqId, smartComponentMap):
        super().smartComponents(reqId, smartComponentMap)
        # print(smartComponentMap)
        # for smartComponent in smartComponentMap:
        #     print("SmartComponent:", smartComponent)


    def typeParser(self, type):
        self.type_Value = ", ".join(type)

        if "BID" in type[1]:
            self.bid_Value = ", ".join(self.bid_Size)
            self.VList.append([self.type_Value + ", " + self.bid_Value])
        elif "ASK" in type[1]:
            self.ask_Value = ", ".join(self.ask_Size)
            self.VList.append([self.type_Value + ", " + self.ask_Value])
        elif "LAST" in type[1]:
            self.last_Value = ", ".join(self.last_Size)
            self.VList.append([self.type_Value + ", " + self.last_Value])
        return self.VList


class Tick(object):
    def __init__(self, type, value):
        self.type = type
        self.value = value


class DTCheck(object):
    def localDT(self):
        tzone = datetime.datetime.now(tzlocal()).tzname()
        if tzone == "China Standard Time":
            date = datetime.datetime.now() - datetime.timedelta(hours=12)   # U.S. East time zone.
        else:
            date = datetime.datetime.now()
        return str(date)


class DBQuery(object):
    def dbRaw(self, date, price, content):
        self.time, self.type, self.value = "'" + date + "'", "'" + content.split(":")[0] + "'", content.split(":")[1]
        self.raw_statement = "INSERT INTO StocksDB.dbo.SPY_Streaming_Raw (timeStamp,attribute,value) VALUES (%s,%s,%s)" % (self.time,self.type,self.value)    # CoreTradingSmall / Stocks
        return self.raw_statement


    def dbExec(self, date, symbol, price, size, bid, ask, volume):
        self.symbol = symbol
        # self.sequence = int(date[23:])
        self.eventYear, self.eventMonth, self.eventDay = int(date[0:4]), int(date[5:7]), int(date[8:10])
        self.eventHour, self.eventMinute, self.eventSecond = int(date[11:13]), int(date[14:16]), int(date[17:19])
        # self.eventMS = int(str(round(float(date[19:]), 3))[2:])

        if self.symbol == "'SPY'":
            self.size, self.targetDB = size * 100, "StocksDB.dbo.SPY_Streaming"    # CoreTradingSmall / Stocks

        self.statement = "INSERT INTO %s (symbol,sequence,exchange,price,size,bidPrice,askPrice,saleConditions,flags,eventYear,eventMonth,eventDay,eventHour,eventMinute,eventSecond,eventMS) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)" \
                            % (self.targetDB,self.symbol,0,0,price,self.size,bid,ask,volume,0,self.eventYear,self.eventMonth,self.eventDay,self.eventHour,self.eventMinute,self.eventSecond,0)
        return self.statement


class SelfRestart(object):
    def reStart(self):
        os.execl(sys.executable, os.path.abspath(__file__), * sys.argv)


class RunApp(object):
    def SPYLoop():
        app_SPY.run()



if __name__ == "__main__":
    currentDT, secDiff = DTCheck().localDT()[0:19], 300    # Give a time period difference to the end of 20:00 for each day.

    # tarTime = currentDT.split(' ')[0] + ' ' + '20:00:00'
    # totalSec = (datetime.datetime.strptime(tarTime, '%Y-%m-%d %H:%M:%S') - datetime.datetime.strptime(currentDT, '%Y-%m-%d %H:%M:%S')).seconds + secDiff

    ''' Subscripition 2:
        SPY live data request, Id starts from 2(20). '''
    app_SPY = IBapi_SPY()
    app_SPY.connect('127.0.0.1', 7497, 2)

    api_thread_SPY = threading.Thread(target=RunApp.SPYLoop, daemon=True)
    api_thread_SPY.start()
    time.sleep(1)   # Sleep interval to allow time for connection to server.

    target_SPY = Contract()
    target_SPY.symbol, target_SPY.secType, target_SPY.exchange, target_SPY.currency = "SPY", "STK", "SMART", "USD"
    app_SPY.reqMktData(2, target_SPY, '', False, False, [])

    ''' Daily seconds: 16.5 Hours from 3:45 to 20:15, totally 16.5 * 3600 = 59400 seconds. Give some margins to the start and end. '''
    time.sleep(59400)    # totalSec
    app_SPY.disconnect()