from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from datetime import datetime, date
from dateutil.tz import tzlocal

import datetime
import threading
import time
import pyodbc

# dbconnection = pyodbc.connect('DRIVER={SQL Server Native Client 11.0}; SERVER=SHIHYAT\ShihYat; DATABASE=Stocks; Trusted_Connection=YES')
# dbconnection = pyodbc.connect('DRIVER={SQL Server Native Client 11.0}; SERVER=76.248.78.74; DATABASE=CoreTradingSmall; UID=sa; PWD=**************')
dbconnection = pyodbc.connect('DRIVER={SQL Server}; SERVER=127.0.0.1; DATABASE=StocksDB; Trusted_Connection=YES')
cursor = dbconnection.cursor()

# Database connection on Agent?(E170).
dbcon_AE170 = pyodbc.connect('DRIVER={SQL Server}; SERVER=70.231.62.170; DATABASE=ALGO_DATA; UID=sa; PWD=**************')
cursor_AE170 = dbcon_AE170.cursor()

class IBapi_VIX(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)

        ''' Set start tag to 0 to ignore data before market open. '''
        self.start_tag, self.vix_value = 0, 0
        self.symbol, self.vix_Time = "NULL", "0000-00-00 00:00:00"


    def error(self, reqId, errorCode, errorString):
        print("VIX Connection Info:", reqId, errorCode, errorString)


    def symbolSamples(self, reqId, contractDescriptions):
        super().symbolSamples(reqId, contractDescriptions)
        if contractDescriptions != []:
            if contractDescriptions[0].contract.symbol == "VIX":
                self.symbol = contractDescriptions[0].contract.symbol


    def currentTime(self, time):
        self.vix_Time = datetime.datetime.fromtimestamp(time).strftime("%Y-%m-%d %H:%M:%S")    # Convert timestamp to datetime format.
        return self.vix_Time


    def tickPrice(self, reqId, tickType, price, attrib):
        self.type = TickTypeEnum.to_str(tickType)
        self.price = Tick(self.type, price)
        self.reqCurrentTime()

        if self.type == "CLOSE":
            self.start_tag = 1

        if self.type == "LAST" and self.vix_value != price and self.start_tag == 1:
            # print(self.vix_Time + ", VIX:", price, end='\n')

            if target_VIX:
                self.symbol_VIX = ("'%s'" % (target_VIX.symbol))
            else:
                self.symbol_VIX = "'VIX'"

            self.vix_raw = DBQuery().dbRaw("StocksDB.dbo.VIX_Streaming_Raw", self.vix_Time, self.type, price)
            cursor.execute(self.vix_raw)
            cursor.connection.commit()

            self.vix_raw_AE170 = DBQuery().dbRaw("ALGO_DATA.dbo.VIX_Streaming_Raw", self.vix_Time, self.type, price)
            cursor_AE170.execute(self.vix_raw_AE170)
            cursor_AE170.connection.commit()

            self.vix_stat = DBQuery().dbExec("StocksDB.dbo.VIX_Streaming", self.vix_Time, self.symbol_VIX, price)
            cursor.execute(self.vix_stat)
            cursor.connection.commit()

            self.vix_stat_AE170 = DBQuery().dbExec("ALGO_DATA.dbo.VIX_Streaming", self.vix_Time, self.symbol_VIX, price)
            cursor_AE170.execute(self.vix_stat_AE170)
            cursor_AE170.connection.commit()

            self.vix_value = price


class Tick(object):
    def __init__(self, type, value):
        self.type = type
        self.value = value


class Timer(object):
    def localTime(self):
        tzone = datetime.datetime.now(tzlocal()).tzname()
        if tzone == "China Standard Time":
            date = datetime.datetime.now() - datetime.timedelta(hours=13)   # U.S. East time zone.
        else:
            date = datetime.datetime.now()
        return str(date)


class DBQuery(object):
    def dbRaw(self, database, date, type, price):
        self.time, self.type, self.value = "'" + date + "'", "'" + type + "'", float(price)
        self.raw_statement = "INSERT INTO %s (timeStamp,attribute,value) VALUES (%s,%s,%s)" % (database,self.time,self.type,self.value)    # CoreTradingSmall / Stocks
        return self.raw_statement


    def dbExec(self, database, date, symbol, price):
        self.symbol = symbol
        # self.sequence = int(date[23:])
        self.eventYear, self.eventMonth, self.eventDay = int(date[0:4]), int(date[5:7]), int(date[8:10])
        self.eventHour, self.eventMinute, self.eventSecond = int(date[11:13]), int(date[14:16]), int(date[17:19])
        # self.eventMS = int(str(round(float(date[19:]), 3))[2:])

        self.statement = "INSERT INTO %s (symbol,price,eventYear,eventMonth,eventDay,eventHour,eventMinute,eventSecond,eventMS) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)" \
                            % (database,self.symbol,price,self.eventYear,self.eventMonth,self.eventDay,self.eventHour,self.eventMinute,self.eventSecond,0)
        return self.statement


class RunApp(object):
    def VIXLoop():
        app_VIX.run()



if __name__ == "__main__":
    recTime, secDiff = Timer().localTime()[0:19], 300    # Give a time period difference to the end of 20:00 for each day.
    # Total seconds calculation of the day.
    tarTime = recTime.split(" ")[0] + " " + "20:00:00"
    totalSec = (datetime.datetime.strptime(tarTime, "%Y-%m-%d %H:%M:%S") - datetime.datetime.strptime(recTime, "%Y-%m-%d %H:%M:%S")).seconds + secDiff

    ''' Subscripition 4:
        VIX live data request, Id starts from 4(40). '''
    app_VIX = IBapi_VIX()
    app_VIX.connect('127.0.0.1', 7497, 4)

    api_thread_VIX = threading.Thread(target=RunApp.VIXLoop, daemon=True)
    api_thread_VIX.start()
    time.sleep(1)   # Sleep interval to allow time for connection to server.

    target_VIX = Contract()
    target_VIX.symbol, target_VIX.secType, target_VIX.exchange, target_VIX.currency = "VIX", "IND", "CBOE", "USD"
    app_VIX.reqMktData(4, target_VIX, '', False, False, [])

    ''' 2025.04.15 Updates: Same as ES, 86300 seconds. '''
    time.sleep(86300)    # totalSec
    app_VIX.disconnect()