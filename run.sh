#!/bin/bash

# =============================================================================
# DATA UPDATE RUNNER SCRIPT
# =============================================================================
# This script runs all data update scripts in sequence
# Author: <PERSON>
# Created: 2025-07-14
# =============================================================================

# Set script directory and change to it
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Add project root to PYTHONPATH for src module imports
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# Activate virtual environment if it exists
if [ -f ".venv/bin/activate" ]; then
    print_status "Activating virtual environment..."
    source ./.venv/bin/activate
else
    print_warning "Virtual environment not found at .venv/bin/activate"
fi

# Detect Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ Python not found. Please ensure Python is installed and in your PATH."
    exit 1
fi

print_status "Using Python command: $PYTHON_CMD"



# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

# Function to run a Python script
run_script() {
    local script_name=$1
    local script_dir=$2
    local script_path="src/scripts/$script_dir/$script_name"
    
    print_status "Running $script_name from $script_dir..."
    
    if [ ! -f "$script_path" ]; then
        print_error "Script not found: $script_path"
        return 1
    fi
    
    # Run the script and capture output
    if $PYTHON_CMD "$script_path"; then
        print_success "$script_name completed successfully"
        return 0
    else
        print_error "$script_name failed"
        return 1
    fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

print_status "Starting data update process..."
echo "=============================================="

# Track overall success
OVERALL_SUCCESS=true

# Run VIX data update
print_status "Running indices.py for VIX..."
if TICKERS="VIX" $PYTHON_CMD src/scripts/indices.py; then
    print_success "indices.py for VIX completed successfully"
else
    print_error "indices.py for VIX failed"
    OVERALL_SUCCESS=false
fi

# Run SPX data update
print_status "Running indices.py for SPX..."
if TICKERS="SPX" $PYTHON_CMD src/scripts/indices.py; then
    print_success "indices.py for SPX completed successfully"
else
    print_error "indices.py for SPX failed"
    OVERALL_SUCCESS=false
fi

# Run NDX data update
print_status "Running indices.py for NDX..."
if TICKERS="NDX" $PYTHON_CMD src/scripts/indices.py; then
    print_success "indices.py for NDX completed successfully"
else
    print_error "indices.py for NDX failed"
    OVERALL_SUCCESS=false
fi

# Run SPY data update
print_status "Running indices.py for SPY..."
if TICKERS="SPY" $PYTHON_CMD src/scripts/indices.py; then
    print_success "indices.py for SPY completed successfully"
else
    print_error "indices.py for SPY failed"
    OVERALL_SUCCESS=false
fi

echo "=============================================="

# Final status
if [ "$OVERALL_SUCCESS" = true ]; then
    print_success "All data updates completed successfully!"
    exit 0
else
    print_error "Some data updates failed. Check the output above."
    exit 1
fi
