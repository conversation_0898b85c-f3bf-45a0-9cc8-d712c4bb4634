import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pyodbc
import datetime as dt
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from dotenv import load_dotenv, find_dotenv
from utils.config import settings

logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(find_dotenv())

# Global database instance for lazy loading
_db_instance = None

def get_db():
    """Get or create database instance (lazy loading)"""
    global _db_instance
    if _db_instance is None:
        _db_instance = MSSQLDatabaseHandler()
    return _db_instance

class MSSQLDatabaseHandler:
    def __init__(self, DB_NAME=settings.DB_ALGO_DATA):
        """Initialize connection to SQL Server using environment variables"""
        self.db_ip = settings.DB_IP
        self.db_user = settings.DB_USER
        self.db_password = settings.DB_PASSWORD
        self.db_name = DB_NAME
        
        self.conn = None
        self.cursor = None
        # Don't connect immediately - use lazy initialization
        logger.info(f"MSSQL Database handler initialized for {self.db_ip}")
        
    def _ensure_connection(self):
        """Ensure database connection is established"""
        if self.conn is None:
            self.connect()
        
    def connect(self):
        """Create connection to SQL Server"""
        try:
            conn_str = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.db_ip};"
                f"DATABASE={self.db_name};"
                f"UID={self.db_user};"
                f"PWD={self.db_password};"
                f"TrustServerCertificate=yes;"
                f"Connection Timeout=30;"
                f"Command Timeout=30;"
            )
            self.conn = pyodbc.connect(conn_str)
            self.cursor = self.conn.cursor()
            logger.info(f"Successfully connected to SQL Server at {self.db_ip}")
        except Exception as e:
            logger.error(f"Error connecting to SQL Server: {str(e)}")
            raise

    def test_connection(self, table_name:str='executed_trades'):
        """Test the connection to the database"""
        try:
            self._ensure_connection()
            sql = f"SELECT count(*) FROM {table_name}"
            self.cursor.execute(sql)
            result = self.cursor.fetchone()
            logger.info(f"Connection test successful: {result[0]} rows found")
            return True
        except Exception as e:
            logger.error(f"Error testing connection: {str(e)}")
            return False

    def get_data(self, table_name):
        """Get all data from a table"""
        try:
            self._ensure_connection()
            sql = f"SELECT * FROM {table_name}"
            self.cursor.execute(sql)
            # Get column names from cursor description
            columns = [column[0] for column in self.cursor.description]
            result = self.cursor.fetchall()
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            return pd.DataFrame(data, columns=columns)
        except Exception as e:
            logger.error(f"Error getting data from {table_name}: {str(e)}")
            return pd.DataFrame()

    def execute_sql(self, sql, params=None):
        """Execute SQL command"""
        try:
            self._ensure_connection()
            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            self.conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error executing SQL: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def create_table_if_not_exists(self, table_name, schema):
        """Create table if it doesn't exist"""
        try:
            self._ensure_connection()
            # Check if table exists
            check_sql = """
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = 'dbo'
            """
            self.cursor.execute(check_sql, (table_name,))
            exists = self.cursor.fetchone()[0] > 0

            if not exists:
                create_sql = f"CREATE TABLE {table_name} ({schema})"
                self.cursor.execute(create_sql)
                self.conn.commit()
                logger.info(f"Created table {table_name}")
                return True
            else:
                logger.info(f"Table {table_name} already exists")
                return True
        except Exception as e:
            logger.error(f"Error creating table {table_name}: {str(e)}")
            return False

    def bulk_insert_dataframe(self, table_name, df):
        """Insert DataFrame into table using bulk insert"""
        try:
            self._ensure_connection()

            # Get column names from DataFrame and escape them with square brackets
            columns = list(df.columns)
            placeholders = ', '.join(['?' for _ in columns])
            escaped_columns = [f'[{col}]' for col in columns]
            column_names = ', '.join(escaped_columns)

            sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"

            # Convert DataFrame to list of tuples
            data = [tuple(row) for row in df.values]

            # Execute bulk insert
            self.cursor.executemany(sql, data)
            self.conn.commit()

            logger.info(f"Successfully inserted {len(data)} rows into {table_name}")
            return True

        except Exception as e:
            logger.error(f"Error bulk inserting into {table_name}: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def close(self):
        """Close database connection"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")

# For backward compatibility, provide a db instance
# But use lazy loading to avoid circular imports
def get_db_instance():
    """Get database instance (backward compatibility)"""
    return get_db()

# Create a db instance for backward compatibility, but don't initialize it immediately
db = None
def _get_db():
    global db
    if db is None:
        db = get_db()
    return db 