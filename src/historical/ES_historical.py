from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from datetime import datetime, timedelta
from dateutil import tz

import json
import os
import sys
import threading
import time
import pyodbc



class IB_His_ES(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)
        self.dataReceived = threading.Event()
        self.dataList = []


    def error(self, reqId, errorCode, errorString):
        if errorCode in [2104, 2106, 2158]:
            return
        print(f"ERROR {reqId} {errorCode}: {errorString}")


    def historicalData(self, reqId: int, bar):
        # print(f"Req {reqId} | {bar.date} | O: {bar.open}, H: {bar.high}, L: {bar.low}, C: {bar.close}, V: {bar.volume}")
        self.dataList.append(bar)


    def historicalDataEnd(self, reqId: int, start: str, end: str):
        print(f"*** Request {reqId} complete. Start: {TimeFormatter().ESTime(datetime.strptime(start, '%Y%m%d %H:%M:%S'))}, End: {TimeFormatter().ESTime(datetime.strptime(end, '%Y%m%d %H:%M:%S'))}. ***")
        self.dataReceived.set()


class CreateContract(object):
    def ES(self, expDate):
        self.contract = Contract()

        self.contract.symbol = "ES"
        self.contract.secType = "FUT"    # CONTFUT
        self.contract.exchange = "CME"
        self.contract.currency = "USD"
        self.contract.lastTradeDateOrContractMonth = expDate

        return self.contract


class DBQuery(object):
    def __init__(self):
        self.connection = pyodbc.connect('DRIVER={SQL Server}; SERVER=70.231.62.170; DATABASE=ALGO_DATA; UID=sa; PWD=**************')
        self.cursor = self.connection.cursor()


    def dbExec(self, dataList):
        self.valueList = [(value.date, value.open, value.high, value.low, value.close, value.volume) for value in dataList]

        self.database = 'ALGO_DATA.dbo.ES_Historical'
        self.statement = f"INSERT INTO {self.database} ([Date],[Open],[High],[Low],[Close],[Volume]) VALUES (?,?,?,?,?,?)"

        try:
            self.cursor.executemany(self.statement, self.valueList)
            self.connection.commit()
        except pyodbc.Error as error:
            print(f"*** Database error: {error}. ***")


class TimeFormatter(object):
    def UTCTime(self, date):
        return date.replace(tzinfo = tz.gettz("America/New_York")).astimezone(tz.UTC)


    def ESTime(self, date):
        return date.replace(tzinfo = tz.UTC).astimezone(tz.gettz("America/New_York")).strftime('%Y-%m-%d %H:%M:%S')


    def TimePeriod(self, start, end):
        if end == '':
            end = datetime.now(tz = tz.UTC)
        else:
            end = self.UTCTime(datetime.strptime(end, '%Y-%m-%d %H:%M:%S'))

        return int((end - start).total_seconds())


class AppRun(object):
    def ESLoop():
        his_ES.run()



if __name__ == "__main__":
    expDate = '202509'
    tar_ES_Contract = CreateContract().ES(expDate)

    startDT = '2025-07-01 00:00:00'
    endDT = '2025-07-25 23:59:59'

    # Set endDT = '' to fetch until present.
    # endDT = ''

    startDT = TimeFormatter().UTCTime(datetime.strptime(startDT, '%Y-%m-%d %H:%M:%S'))
    timePeriod = TimeFormatter().TimePeriod(startDT, endDT)
    execRounds = timePeriod // 1800 + 1

    print(f'''*** Preparing to fetch Historical data of {tar_ES_Contract.symbol},
    Start: {startDT}, End: {endDT},
    Time Period: {timePeriod} seconds, Execution Rounds: {execRounds}. ***''')

    # ''' Subscripition ES historical data:
    #     ES historical data request, request Id: 7(70). '''
    his_ES = IB_His_ES()
    his_ES.connect('127.0.0.1', 7497, 7)

    es_Thread = threading.Thread(target = AppRun.ESLoop, daemon = True)
    es_Thread.start()
    time.sleep(1)

    totalBars = 0

    for i in range(execRounds):
        his_ES.dataReceived.clear()
        his_ES.dataList.clear()

        timeStep = f"{(startDT + timedelta(minutes = 30 * (i + 1))).strftime('%Y%m%d-%H:%M:%S')}"

        HDValues = his_ES.reqHistoricalData(
            i + 1,    # Request ID.
            tar_ES_Contract,
            endDateTime = timeStep,    # Set to empty when secType = 'CONTFUT'.
            durationStr = '1800 S',
            barSizeSetting = '1 secs',
            whatToShow = 'Trades',
            useRTH = False,
            formatDate = 1,    # 2 is timestamp.
            keepUpToDate = False,
            chartOptions = []
        )

        his_ES.dataReceived.wait(timeout = 10)
        totalBars += len(his_ES.dataList)

        DBQuery().dbExec(his_ES.dataList)

        # API restriction:
        # Pacing Violations for Small Bars (30 secs or less).
        # Making identical historical data requests within 15 seconds.
        # Making six or more historical data requests for the same Contract, Exchange and Tick Type within 2 seconds.
        # Making more than 60 requests within any 10 minute period.
        time.sleep(11.5)

    print(f"*** Total 1-sec bars collected from {startDT} to {endDT}: {totalBars}. ***")
    his_ES.disconnect()