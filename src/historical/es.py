import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
import asyncio
from datetime import datetime, timedelta, time as dtime
import pandas as pd
from ib_insync import *
import pytz
import calendar

# Optionally, import connect_to_ib if you want to use the shared async connection logic
from src.utils.ibkr_service import connect_to_ib
from src.db.database import get_db

def get_listed_es_contracts(reference_date=None):
    """
    Returns a list of expiry strings (YYYYMM) for the next 8 quarterly ES contracts
    plus 3 additional December contracts, starting from the reference_date.
    """
    if reference_date is None:
        reference_date = datetime.now()
    months = [3, 6, 9, 12]
    contracts = []
    year = reference_date.year
    month = reference_date.month

    # Find the next quarterly contract month
    next_quarter = min([m for m in months if m >= month], default=3)
    if next_quarter < month:
        year += 1

    # Generate next 8 quarters
    y, m = year, next_quarter
    for _ in range(8):
        contracts.append(f"{y}{m:02d}")
        m += 3
        if m > 12:
            m = m % 12
            y += 1

    # Add next 3 December contracts after the last one
    last_year = int(contracts[-1][:4])
    dec_years = [y for y in range(last_year + 1, last_year + 4)]
    for y in dec_years:
        contracts.append(f"{y}12")

    return contracts

def get_es_expiry_for_date(date):
    """
    Given a date, return the expiry string (YYYYMM) for the front month ES contract.
    ES contracts expire on the 3rd Friday of March, June, September, December.
    The front month is the next expiry that is not expired for the given date,
    considering the 8 consecutive quarters and 3 additional December contracts rule.
    """
    months = [3, 6, 9, 12]
    year = date.year
    expiry_dates = []
    for m in months:
        # 3rd Friday of the month
        c = calendar.Calendar(firstweekday=calendar.SUNDAY)
        monthcal = c.monthdatescalendar(year, m)
        third_friday = [day for week in monthcal for day in week if \
            day.weekday() == calendar.FRIDAY and day.month == m][2]
        expiry_dates.append((third_friday, m, year))
    # If date is after Dec expiry, next expiry is next year's March
    for i, (exp_date, m, y) in enumerate(expiry_dates):
        if date.date() <= exp_date:
            expiry = f"{y}{m:02d}"
            break
    else:
        # If not found, must be after Dec expiry
        expiry = f"{year+1}03"

    # Generate all currently listed contracts
    listed_contracts = get_listed_es_contracts(date)
    # Find the first listed contract that is not expired for the given date
    for exp in listed_contracts:
        exp_year = int(exp[:4])
        exp_month = int(exp[4:])
        # Find 3rd Friday for this expiry
        c = calendar.Calendar(firstweekday=calendar.SUNDAY)
        monthcal = c.monthdatescalendar(exp_year, exp_month)
        third_friday = [day for week in monthcal for day in week if \
            day.weekday() == calendar.FRIDAY and day.month == exp_month][2]
        if date.date() <= third_friday:
            return exp
    # If not found, return the last listed contract
    return listed_contracts[-1]

def daterange(start_date, end_date):
    for n in range(int((end_date - start_date).days) + 1):
        yield start_date + timedelta(n)

async def get_front_month_es_contract(ib, day):
    """
    For a given day, return the ES contract that is active and tradable on that day.
    Only return contracts whose trading period includes the given day.
    """
    expiry = get_es_expiry_for_date(day)
    contract = Future('ES', expiry, 'GLOBEX')
    contract.includeExpired = True
    details = await ib.reqContractDetailsAsync(contract)
    day_date = day.date()
    valid_contracts = []
    for detail in details:
        expiry_str = detail.contract.lastTradeDateOrContractMonth
        # Parse expiry date
        try:
            if len(expiry_str) >= 8:
                expiry_date = datetime.strptime(expiry_str[:8], '%Y%m%d').date()
            else:
                expiry_date = datetime.strptime(expiry_str, '%Y%m').date()
        except Exception:
            continue
        # Parse trading hours (format: 'YYYYMMDD:HHMM-YYYYMMDD:HHMM;...')
        trading_hours = detail.tradingHours.split(';') if detail.tradingHours else []
        is_tradable = False
        for period in trading_hours:
            if not period:
                continue
            try:
                date_part = period.split(':')[0]
                period_date = datetime.strptime(date_part, '%Y%m%d').date()
                if period_date == day_date:
                    is_tradable = True
                    break
            except Exception:
                continue
        # Only consider contracts that have not expired and are tradable on this day
        if expiry_date >= day_date and is_tradable:
            valid_contracts.append((expiry_date, detail.contract, expiry_str))
    # Return the nearest expiry contract that is valid for this day
    if valid_contracts:
        valid_contracts.sort()  # sort by expiry_date
        _, contract, expiry_str = valid_contracts[0]
        return contract, expiry_str
    return None, None

async def fetch_es_data():
    ib = await connect_to_ib()
    if ib is None:
        print("Failed to connect to IB.")
        return
    try:
        end = datetime.now()
        start = end - timedelta(days=730)
        today = datetime.now()
        cst = pytz.timezone('America/Chicago')
        est = pytz.timezone('America/New_York')
        all_rows = []
        print("Fetching 10 sec bars for each trading day in the last 2 years. This may take a while...")
        for day in daterange(start, end):
            if day > today:
                break
            if day.weekday() >= 5:
                continue  # skip weekends
            contract, expiry = await get_front_month_es_contract(ib, day)
            if not contract or not expiry:
                print(f"No valid ES contract for {day.date()}")
                continue
            try:
                await ib.qualifyContractsAsync(contract)
            except Exception as e:
                print(f"Failed to qualify contract for {day.date()} ({expiry}): {e}")
                continue
            # IB API expects endDateTime as end of day for the date
            end_dt = day.replace(hour=23, minute=59, second=59)
            try:
                bars = await ib.reqHistoricalDataAsync(
                    contract,
                    endDateTime=end_dt.strftime('%Y%m%d %H:%M:%S'),
                    durationStr='1 D',
                    barSizeSetting='10 secs',
                    whatToShow='TRADES',
                    useRTH=False,
                    formatDate=1
                )
            except Exception as e:
                print(f"Failed to fetch data for {day.date()} ({expiry}): {e}")
                continue
            if not bars:
                print(f"No data for {day.date()} ({expiry})")
                continue
            for bar in bars:
                all_rows.append({
                    'date': bar.date.astimezone(cst).astimezone(est).replace(tzinfo=None),
                    'open': bar.open,
                    'high': bar.high,
                    'low': bar.low,
                    'close': bar.close,
                    'volume': bar.volume,
                    'expiry': expiry
                })
            print(f"Fetched {len(bars)} bars for {day.date()} ({expiry})")
            await asyncio.sleep(0.5)  # avoid pacing violations
        if not all_rows:
            print("No data fetched.")
            return
        df = pd.DataFrame(all_rows)
        db = get_db()
        print("Writing data to ES_data table in ALGO_DATA database...")
        success = db.bulk_insert_dataframe('ES', df)
        if success:
            print("Successfully wrote data to ES table.")
        else:
            print("Failed to write data to ES table.")
    finally:
        ib.disconnect()
        del ib

if __name__ == "__main__":
    asyncio.run(fetch_es_data())