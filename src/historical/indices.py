import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

import os
import asyncio
from datetime import datetime, timedelta
from dotenv import load_dotenv
from src.utils.ibkr_service import connect_to_ib, get_historical_data, get_close_price_yahoo
from src.db.database import get_db

# Load environment variables
load_dotenv()

def get_start_of_last_year():
    today = datetime.now()
    return datetime(today.year - 1, 1, 1)

def get_yesterday():
    return datetime.now() - timedelta(days=1)

async def fetch_and_save_spx_data(symbol='SPX'):
    start_date = get_start_of_last_year()
    end_date = get_yesterday()
    db = get_db()
    db.create_table_if_not_exists(symbol, "date DATETIME, open FLOAT, high FLOAT, low FLOAT, close FLOAT, volume FLOAT")  # Example schema, adjust as needed
    ib = await connect_to_ib()
    if not ib:
        print("Failed to connect to IBKR Gateway. Exiting.")
        return
    try:
        df_existing = db.get_data('SPX')

        current = start_date
        while current <= end_date:
            if current.weekday() < 5:  # Only weekdays
                # Check if data already exists for this date
                # You may want to adjust this logic to match your table schema
                exists = False
                if not df_existing.empty:
                    exists = any((df_existing['date'] >= current.replace(hour=0, minute=0, second=0)) & (df_existing['date'] < (current + timedelta(days=1)).replace(hour=0, minute=0, second=0)))
                if exists:
                    print(f"Data already exists for {current.strftime('%Y-%m-%d')}, skipping...")
                else:
                    print(f"Fetching data for {current.strftime('%Y-%m-%d')}")
                    df = await get_historical_data(ib, symbol, current, bar_size='10 secs')
                    if df is not None:
                        try:
                            df_yahoo = await get_close_price_yahoo(symbol, current)
                            if df_yahoo is not None and not df_yahoo.empty:
                                yahoo_close = float(df_yahoo['close'].iloc[0])
                                target_time = current.replace(hour=15, minute=59, second=50, microsecond=0)
                                mask = df['date'] == target_time
                                df.loc[mask, 'close'] = yahoo_close
                            db.bulk_insert_dataframe('SPX', df)
                            print(f"Successfully saved data for {current.strftime('%Y-%m-%d')} to database")
                        except Exception as e:
                            print(f"Error saving data for {current.strftime('%Y-%m-%d')} to database: {e}")
                    else:
                        print(f"No data returned for {current.strftime('%Y-%m-%d')}")
                    await asyncio.sleep(15)  # Avoid rate limiting
            current += timedelta(days=1)
    finally:
        db.close()
        ib.disconnect()
        print("Disconnected from IBKR Gateway and closed DB connection.")

if __name__ == "__main__":
    symbol = os.getenv('TICKERS', 'SPX')
    tickers = symbol.split(',')
    for ticker in tickers:
        asyncio.run(fetch_and_save_spx_data(ticker)) 