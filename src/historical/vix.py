#!/usr/bin/env python3
"""
VIX Data Fetcher from Yahoo Finance

This script fetches daily VIX (Volatility Index) data from Yahoo Finance
for the last 2 years and saves it to a CSV file.

Requirements:
    pip install yfinance pandas

Usage:
    python VIX_yahoo.py
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from src.db.database import get_db

def fetch_vix_data(period_years=2):
    """
    Fetch VIX data from Yahoo Finance for the specified period.

    Args:
        period_years (int): Number of years of historical data to fetch

    Returns:
        pandas.DataFrame: DataFrame containing VIX data with columns:
                         Date, Open, High, Low, Close, Volume
    """
    try:
        # Calculate start date (2 years ago from today)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period_years * 365)

        print(f"Fetching VIX data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Create ticker object for VIX
        vix_ticker = yf.Ticker("^VIX")

        # Fetch historical data - add one day to end_date to include the current date
        # since yfinance end parameter is exclusive
        end_date_inclusive = end_date + timedelta(days=1)
        
        vix_data = vix_ticker.history(
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date_inclusive.strftime('%Y-%m-%d'),
            interval='1d'
        )

        if vix_data.empty:
            raise ValueError("No data retrieved from Yahoo Finance")

        # Reset index to make Date a column
        vix_data.reset_index(inplace=True)

        # Format the Date column
        vix_data['Date'] = vix_data['Date'].dt.strftime('%Y-%m-%d')

        # Round numerical values to 2 decimal places
        numerical_columns = ['Open', 'High', 'Low', 'Close']
        for col in numerical_columns:
            if col in vix_data.columns:
                vix_data[col] = vix_data[col].round(2)

        print(f"Successfully fetched {len(vix_data)} days of VIX data")
        return vix_data

    except Exception as e:
        print(f"Error fetching VIX data: {str(e)}")
        return None


def get_existing_dates(table_name="VIX"):
    """
    Get existing dates from the database table.

    Args:
        table_name (str): Database table name

    Returns:
        set: Set of existing dates
    """
    try:
        db = get_db()

        # Check if table exists first
        check_sql = """
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = ? AND TABLE_SCHEMA = 'dbo'
        """
        db._ensure_connection()
        db.cursor.execute(check_sql, (table_name,))
        table_exists = db.cursor.fetchone()[0] > 0

        if not table_exists:
            print(f"Table {table_name} doesn't exist yet")
            return set()

        # Get existing dates
        sql = f"SELECT [Date] FROM {table_name}"
        db.cursor.execute(sql)
        existing_dates = {row[0] for row in db.cursor.fetchall()}
        print(f"Found {len(existing_dates)} existing dates in database")
        return existing_dates

    except Exception as e:
        print(f"Error getting existing dates: {str(e)}")
        return set()
    # Note: Don't close the global database instance - it's shared across functions


def save_to_database(data, table_name="VIX"):
    """
    Save VIX data to ALGO_DATA database, skipping existing dates.

    Args:
        data (pandas.DataFrame): VIX data to save
        table_name (str): Database table name
    """
    try:
        db = get_db()

        # Create table schema for VIX data
        schema = """
            [Date] DATE NOT NULL,
            [Open] DECIMAL(10,2),
            [High] DECIMAL(10,2),
            [Low] DECIMAL(10,2),
            [Close] DECIMAL(10,2),
            PRIMARY KEY ([Date])
        """

        # Create table if it doesn't exist
        if not db.create_table_if_not_exists(table_name, schema):
            print(f"❌ Failed to create table {table_name}")
            return False

        # Get existing dates
        existing_dates = get_existing_dates(table_name)

        # Convert Date column to proper format for SQL Server
        data_copy = data.copy()
        data_copy['Date'] = pd.to_datetime(data_copy['Date']).dt.date

        # Filter out existing dates
        if existing_dates:
            initial_count = len(data_copy)
            data_copy = data_copy[~data_copy['Date'].isin(existing_dates)]
            filtered_count = len(data_copy)
            skipped_count = initial_count - filtered_count

            if skipped_count > 0:
                print(f"⏭️  Skipped {skipped_count} existing dates")

            if filtered_count == 0:
                print(f"✅ All data already exists in database - nothing to insert")
                return True

        # Insert new data only
        if db.bulk_insert_dataframe(table_name, data_copy):
            print(f"✅ Inserted {len(data_copy)} new records into database table: {table_name}")
            return True
        else:
            print(f"❌ Failed to insert data into {table_name}")
            return False

    except Exception as e:
        print(f"❌ Error saving data to database: {str(e)}")
        return False
    # Note: Don't close the global database instance - it's shared across functions


def save_to_csv(data, filename="vix_data.csv"):
    """
    Save VIX data to CSV file as a fallback when database is unavailable.
    
    Args:
        data (pandas.DataFrame): VIX data to save
        filename (str): CSV filename
    """
    try:
        data.to_csv(filename, index=False)
        print(f"✅ Saved {len(data)} records to CSV file: {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving to CSV: {str(e)}")
        return False


def display_summary(data):
    """
    Display summary statistics of the VIX data.

    Args:
        data (pandas.DataFrame): VIX data
    """
    if data is None or data.empty:
        print("No data to display")
        return

    print("\n" + "="*50)
    print("VIX DATA SUMMARY")
    print("="*50)

    print(f"Date Range: {data['Date'].iloc[0]} to {data['Date'].iloc[-1]}")
    print(f"Total Trading Days: {len(data)}")

    if 'Close' in data.columns:
        print(f"\nVIX Close Price Statistics:")
        print(f"  Current (Latest): {data['Close'].iloc[-1]:.2f}")
        print(f"  Minimum: {data['Close'].min():.2f}")
        print(f"  Maximum: {data['Close'].max():.2f}")
        print(f"  Average: {data['Close'].mean():.2f}")
        print(f"  Median: {data['Close'].median():.2f}")

    print(f"\nFirst 5 rows:")
    print(data.head())

    print(f"\nLast 5 rows:")
    print(data.tail())


def main():
    """Main function to execute the VIX data fetching process."""
    print("VIX Data Fetcher - Yahoo Finance")
    print("="*40)

    # Fetch VIX data for last 2 years
    vix_data = fetch_vix_data(period_years=2)

    if vix_data is not None:
        # Drop unwanted columns (ignore if they don't exist)
        vix_data.drop(columns=['Volume', 'Dividends', 'Stock Splits'], inplace=True, errors='ignore')

        # Display summary
        display_summary(vix_data)

        # Try to save to database first
        if save_to_database(vix_data):
            print(f"\n✅ Process completed successfully!")
        else:
            print(f"\n⚠️  Database save failed, saving to CSV instead...")
            if save_to_csv(vix_data):
                print(f"\n✅ Data saved to CSV successfully!")
            else:
                print(f"\n❌ Failed to save data to both database and CSV")
    else:
        print("❌ Failed to fetch VIX data")


if __name__ == "__main__":
    main()