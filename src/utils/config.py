import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    # Database settings
    DB_IP: str = os.getenv('DB_IP', 'localhost')
    DB_USER: str = os.getenv('DB_USER', '')
    DB_PASSWORD: str = os.getenv('DB_PASSWORD', '')
    DB_ALGO_DATA: str = os.getenv('DB_NAME', 'ALGO_DATA')
    DB_IBDB_DEV: str = os.getenv('DB_IBDB_DEV', 'IBDB_DEV')
    DB_ALGO_APP: str = os.getenv('DB_ALGO_APP', 'ALGO_APP')
    
    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "JerryVision"
    API_BASE_URL: str = os.getenv('API_BASE_URL', 'http://localhost:8000')
    
    def __init__(self):
        pass

settings = Settings() 