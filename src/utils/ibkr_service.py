import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import os
from ib_insync import *
import pandas as pd
from datetime import datetime, timedelta
import time
import pytz
from src.db.database import get_db
import asyncio
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
import yfinance as yf


# Create a thread pool for IB operations
thread_pool = ThreadPoolExecutor(max_workers=1)

async def connect_to_ib():
    """Connect to IB Gateway running on localhost:4015"""
    ib = IB()

    host = os.getenv('IB_HOST', '127.0.0.1')
    port = int(os.getenv('IB_PORT', '4015'))
    client_id = int(os.getenv('IB_CLIENT_ID', '1'))
    try:
        await ib.connectAsync(host, port, clientId=client_id)
        print("Successfully connected to IB Gateway")
        return ib
    except Exception as e:
        print(f"Failed to connect to IB Gateway: {e}")
        return None

async def get_historical_data(ib, symbol, date, bar_size='10 secs', secType='IND', exchange='CBOE', currency='USD'):
    """
    Get historical data for a specific contract and date
    
    Parameters:
    - symbol: The ticker symbol (e.g., 'SPX')
    - date: The specific date to fetch data for
    - bar_size: Size of each bar (default: '10 secs')
    - secType: Security type (default: 'IND' for indices)
    - exchange: Exchange (default: 'CBOE' for SPX)
    - currency: Currency (default: 'USD')
    """
    # Create contract
    contract = Index(symbol, exchange, currency)
    
    # Format the date for IB API - use end of day
    end_date = date.replace(hour=23, minute=59, second=59).strftime('%Y%m%d %H:%M:%S')
    
    # Request historical data for just this day
    await ib.qualifyContractsAsync(contract)
    bars = await ib.reqHistoricalDataAsync(
        contract,
        endDateTime=end_date,
        durationStr='1 D',  # Get one day of data
        barSizeSetting=bar_size,
        whatToShow='TRADES',
        useRTH=False,
        formatDate=1
    )
    
    if not bars:
        print(f"No historical data received for {symbol} on {date}")
        return None
    
    # Filter bars to only include the specific date
    target_date = date.date()
    filtered_bars = [bar for bar in bars if bar.date.date() == target_date]
    
    if not filtered_bars:
        print(f"No data found for {symbol} on {date}")
        return None
    
    # Create DataFrame with historical data and convert timezone from CST to EST
    cst = pytz.timezone('America/Chicago')
    est = pytz.timezone('America/New_York')
    
    df = pd.DataFrame({
        'date': [bar.date.astimezone(cst).astimezone(est).replace(tzinfo=None) for bar in filtered_bars],
        'open': [bar.open for bar in filtered_bars],
        'high': [bar.high for bar in filtered_bars],
        'low': [bar.low for bar in filtered_bars],
        'close': [bar.close for bar in filtered_bars],
    })
    
    return df

 

async def get_close_price_yahoo(symbol: str = 'SPX', date: datetime = None) -> pd.DataFrame:
    """
    Fetch the daily close price for a symbol from Yahoo Finance for a given date.
    Returns a pandas DataFrame with columns: date, close
    """
    if date is None:
        date = datetime.today()
    
    ticker = f'^{symbol}'

    start = date.strftime('%Y-%m-%d')
    end = (date + pd.Timedelta(days=1)).strftime('%Y-%m-%d')
    
    df = yf.download(ticker, start=start, end=end, interval='1d', progress=False, auto_adjust=True)
    
    if df.empty:
        return pd.DataFrame()
    
    df = df.reset_index()
    df = df.rename(columns={'Date': 'date', 'Close': 'close'})
    df = df[['date', 'close']]
    

    return df
